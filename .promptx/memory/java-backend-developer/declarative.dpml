<?xml version="1.0" encoding="UTF-8"?>
<memory>
  <item id="mem_1753424469374_rmhrwiiy9" time="2025/07/25 14:21">
    <content>
      Java团队开发完整指南核心规范：
      1. 强制使用构造函数依赖注入，严禁字段注入
      2. 私有化常量使用private static final修饰，全大写下划线命名
      3. 命名必须清晰准确，做到&quot;望文生义&quot;
      4. 严格限制反射使用，99%属性访问通过getter/setter
      5. 避免不必要中间变量，直接返回表达式结果
      6. Stream API优先处理集合操作
      7. 链式调用与Builder模式优先
      8. 严禁N+1查询，使用JOIN FETCH或批量查询
      9. 常量类管理，不允许魔法值
      10. 异常处理规范，不允许空catch块
      11. 不允许写单元测试（团队规定）
      12. DRY原则，避免重复代码
      13. 拒绝无意义注释
      14. Lombok注解优先使用
      15. switch语法替代多个else if
    </content>
    <tags>#其他</tags>
  </item>
  <item id="mem_1753424496910_pzco04vux" time="2025/07/25 14:21">
    <content>
      四层架构开发模式和技术栈标准：
      架构层次：Controller层(HTTP交互、参数校验、响应封装) → DTO层(请求/响应数据结构) → Service层(核心业务逻辑、事务管理) → Repository层(数据持久化操作)
      技术栈：Spring Boot 3.2.x + JPA + PostgreSQL + Redis
      RESTful API规范：资源使用复数名词，GET查询、POST创建、PUT完全更新、PATCH部分更新、DELETE删除
      参数校验：使用jakarta.validation注解，@Valid触发DTO校验
      全局异常处理：@RestControllerAdvice定义全局异常处理器，优先使用BusinessException和ExceptionEnum
      Java8+特性：LocalDateTime时间处理、Optional判空、Stream API集合处理、函数式接口使用
      Hutool工具类：StrUtil字符串处理、CollUtil集合操作、BeanUtil属性拷贝、LocalDateTimeUtil日期处理、NumberUtil数值计算
    </content>
    <tags>#工具使用</tags>
  </item>
  <item id="mem_1753428499119_4tahxfql0" time="2025/07/25 15:28">
    <content>
      完成了inner-ai项目的用户管理模块CRUD开发：
      1. 创建了完整的四层架构：UserController、UserService/UserServiceImpl、UserRepository、User实体
      2. 实现了用户的增删改查、分页查询、状态管理等功能
      3. 完善了全局异常处理器GlobalExceptionHandler，支持各种异常类型的统一处理
      4. 添加了JPA审计配置和Flyway数据库迁移支持
      5. 创建了UserSpecification查询规格工具类用于复杂查询
      6. 严格遵循了团队开发规范：构造函数注入、LocalDateTime时间处理、Hutool工具类使用、Stream API等
      7. 数据库表结构基于V1__Create_users_table.sql，包含id、name、email、phone、status等字段
      8. 响应统一使用ResponseBean&lt;T&gt;格式，异常处理使用BusinessException和ExceptionEnum
    </content>
    <tags>#工具使用</tags>
  </item>
  <item id="mem_1753428810206_ktlqugmsv" time="2025/07/25 15:33">
    <content>
      修改了所有Controller返回类型，统一使用ResponseBean而不是ResponseEntity&lt;ResponseBean&gt;：
      1. UserController的所有方法都改为直接返回ResponseBean&lt;T&gt;
      2. HealthController的health方法改为返回ResponseBean&lt;Map&lt;String, Object&gt;&gt;
      3. GlobalExceptionHandler的所有异常处理方法都改为返回ResponseBean&lt;Object&gt;
      4. 移除了不必要的ResponseEntity和HttpStatus导入
      5. 简化了代码结构，直接返回ResponseBean.success()或ResponseBean.failed()
      这样更符合团队规范，响应格式更加统一简洁
    </content>
    <tags>#其他</tags>
  </item>
  <item id="mem_1753429078273_hpiyg72ne" time="2025/07/25 15:37">
    <content>
      重构UserServiceImpl和GlobalExceptionHandler，严格遵循团队开发规范：
      1. 判空处理：将所有 != null 改为使用Hutool工具类，如ObjectUtil.isNotNull()、ArrayUtil.isNotEmpty()
      2. 提取重复代码：从UserServiceImpl中提取了5个公共方法：
      - findUserByIdOrThrow(): 根据ID查找用户，统一异常处理
      - validateEmailNotExists(): 校验邮箱是否已存在
      - validateEmailNotExistsForUpdate(): 校验邮箱更新时是否冲突
      - updateUserFields(): 统一更新用户字段逻辑
      - updateUserStatus(): 统一更新用户状态并保存
      3. 使用ObjectUtil.defaultIfNull()替代Optional.ofNullable().orElse()
      4. 代码更简洁，消除了重复的userRepository.findById()调用
      5. 遵循DRY原则，提高了代码的可维护性和可读性
    </content>
    <tags>#工具使用</tags>
  </item>
  <item id="mem_1753429880619_8bc7x1ci2" time="2025/07/25 15:51">
    <content>
      进一步优化判空处理，使用更合适的Hutool工具类：
      1. 将ObjectUtil.isNotNull(request.getStatus())改为BeanUtil.isNotEmpty(request.getStatus())
      2. BeanUtil.isNotEmpty()更适合判断对象是否为空，特别是对于枚举类型
      3. 添加了BeanUtil导入，完善了Hutool工具类的使用
      4. 在GlobalExceptionHandler中规范了ArrayUtil的导入和使用
      5. 这样更符合Hutool工具类的最佳实践，BeanUtil专门用于Bean相关的操作和判空
    </content>
    <tags>#最佳实践 #工具使用</tags>
  </item>
  <item id="mem_1753432819824_r7aeylj41" time="2025/07/25 16:40">
    <content>
      学习了新的参数校验规范：
      1. 必须在Controller层或Service层入口处使用jakarta.validation注解对DTO进行校验
      2. Controller类必须添加@Validated注解
      3. 请求体参数使用@Valid @RequestBody触发DTO内部校验注解
      4. 路径参数直接使用校验注解如@Min、@Max等
      5. 查询参数使用@Valid触发校验
      6. 优先采用Group Validation进行分组校验，如Add.class、Edit.class等接口
      7. DTO中定义校验分组接口：public interface Add{} public interface Edit{}
      8. 校验注解可以指定groups属性进行分组：@NotBlank(groups = {Add.class, Edit.class})
      9. 这样可以在不同操作场景下应用不同的校验规则，提高校验的灵活性和准确性
    </content>
    <tags>#最佳实践</tags>
  </item>
  <item id="mem_1753432925143_5fmzcc9pm" time="2025/07/25 16:42">
    <content>
      实现了完整的参数校验规范，采用Group Validation分组校验：
      1. 为所有DTO添加了校验分组接口：CreateUserRequest.Create、UpdateUserRequest.Update、UserPageRequest.Query
      2. 所有校验注解都指定了groups属性，实现分组校验
      3. Controller中使用@Validated(分组.class)替代@Valid，实现精确的分组校验
      4. 路径参数添加了@Min校验注解，确保ID大于0
      5. 查询参数使用@Validated(Query.class)进行校验
      6. 这样实现了不同操作场景下的差异化校验规则，提高了校验的灵活性和准确性
      7. 符合团队规范：优先采用Group Validation进行分组校验
    </content>
    <tags>#最佳实践</tags>
  </item>
  <item id="mem_1753433122268_i1kikq3g8" time="2025/07/25 16:45">
    <content>
      重构DTO类，遵循DRY原则，合并重复的请求DTO：
      1. 将CreateUserRequest和UpdateUserRequest合并为统一的UserRequest类
      2. 在UserRequest中定义两个校验分组：Create和Update
      3. 通过groups属性区分不同操作的校验规则：
      - Create分组：用户名和邮箱必填(@NotBlank)
      - Update分组：所有字段都是可选的，只校验格式
      - 共同校验：字符长度、邮箱格式、手机号格式等
      4. 删除了重复的DTO文件，减少了代码冗余
      5. 更新了Controller、Service接口和实现类的方法签名
      6. 这样既遵循了DRY原则，又充分利用了Group Validation的优势
      7. 一个DTO类通过分组校验支持多种业务场景，代码更简洁易维护
    </content>
    <tags>#最佳实践</tags>
  </item>
</memory>