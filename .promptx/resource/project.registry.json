{"version": "2.0.0", "source": "project", "metadata": {"version": "2.0.0", "description": "project 级资源注册表", "createdAt": "2025-07-25T07:22:52.594Z", "updatedAt": "2025-07-25T07:22:52.606Z", "resourceCount": 3}, "resources": [{"id": "java-development-workflow", "source": "project", "protocol": "execution", "name": "Java Development Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/java-backend-developer/execution/java-development-workflow.execution.md", "metadata": {"createdAt": "2025-07-25T07:22:52.599Z", "updatedAt": "2025-07-25T07:22:52.599Z", "scannedAt": "2025-07-25T07:22:52.599Z", "path": "role/java-backend-developer/execution/java-development-workflow.execution.md"}}, {"id": "java-backend-developer", "source": "project", "protocol": "role", "name": "Java Backend Developer 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/java-backend-developer/java-backend-developer.role.md", "metadata": {"createdAt": "2025-07-25T07:22:52.601Z", "updatedAt": "2025-07-25T07:22:52.601Z", "scannedAt": "2025-07-25T07:22:52.601Z", "path": "role/java-backend-developer/java-backend-developer.role.md"}}, {"id": "java-backend-thinking", "source": "project", "protocol": "thought", "name": "Java Backend Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/java-backend-developer/thought/java-backend-thinking.thought.md", "metadata": {"createdAt": "2025-07-25T07:22:52.604Z", "updatedAt": "2025-07-25T07:22:52.604Z", "scannedAt": "2025-07-25T07:22:52.604Z", "path": "role/java-backend-developer/thought/java-backend-thinking.thought.md"}}], "stats": {"totalResources": 3, "byProtocol": {"execution": 1, "role": 1, "thought": 1}, "bySource": {"project": 3}}}