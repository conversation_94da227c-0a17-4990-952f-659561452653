<execution>
  <constraint>
    ## 团队开发硬性约束
    - **技术栈限制**：必须使用Spring Boot 3.2.x + JPA + PostgreSQL + Redis
    - **依赖注入约束**：强制使用构造函数注入，禁止字段注入和Setter注入
    - **时间处理约束**：强制使用LocalDateTime系列，禁止Date/Calendar
    - **工具类约束**：优先使用Hutool工具类，禁止原生null判断
    - **测试约束**：按团队规定不生成单元测试代码
    - **异常处理约束**：必须使用全局异常处理，禁止空catch块
  </constraint>

  <rule>
    ## 强制性开发规则
    - **构造函数依赖注入规则**：所有Service类必须使用构造函数注入依赖
    - **常量定义规则**：类级别常量必须private static final修饰，全大写下划线命名
    - **命名规则**：所有变量、方法、类名必须清晰反映用途，做到"望文生义"
    - **Stream API规则**：集合操作优先使用Stream API，避免传统for循环
    - **Optional规则**：可能为null的值必须使用Optional处理
    - **Hutool规则**：字符串判空使用StrUtil，集合判空使用CollUtil
    - **四层架构规则**：严格遵循Controller-DTO-Service-Repository分层
    - **RESTful规则**：API设计必须遵循RESTful规范
  </rule>

  <guideline>
    ## 开发指导原则
    - **代码简洁性**：避免不必要的中间变量，直接返回表达式结果
    - **链式调用优先**：复杂对象构建使用Builder模式和链式调用
    - **DRY原则**：发现重复代码立即提取为公共方法
    - **性能意识**：时刻警惕N+1查询，使用JOIN FETCH或批量查询
    - **异常处理完善**：记录详细日志，提供友好错误信息
    - **Lombok使用**：能用Lombok注解的地方就使用，减少样板代码
    - **Switch优先**：多重判断使用switch case，避免多个else if
  </guideline>

  <process>
    ## Java后端开发标准流程
    
    ### Phase 1: 需求分析与设计 (15分钟)
    ```mermaid
    flowchart LR
        A[需求分析] --> B[API设计]
        B --> C[数据模型设计]
        C --> D[技术方案确定]
    ```
    
    **关键检查点**：
    - API是否符合RESTful规范？
    - 数据模型是否合理？
    - 是否考虑了性能和扩展性？
    
    ### Phase 2: 数据层实现 (20分钟)
    ```mermaid
    flowchart TD
        A[Entity设计] --> B[Repository接口]
        B --> C[复杂查询Specification]
        C --> D[数据访问测试]
    ```
    
    **实现标准**：
    ```java
    // Entity标准模板
    @Entity
    @Table(name = "users")
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Getter
    @Setter
    public class User extends EntityBase {
        @Column(nullable = false, length = 50)
        private String name;
        
        @Column(nullable = false, unique = true, length = 100)
        private String email;
        
        @Enumerated(EnumType.STRING)
        @Column(nullable = false, length = 20)
        private UserStatus status;
    }
    
    // Repository标准模板
    public interface UserRepository extends JpaRepository<User, String>, JpaSpecificationExecutor<User> {
        Optional<User> findByEmail(String email);
        List<User> findByStatus(UserStatus status);
    }
    ```
    
    ### Phase 3: 业务层实现 (30分钟)
    ```mermaid
    flowchart TD
        A[Service接口定义] --> B[Service实现]
        B --> C[业务逻辑编写]
        C --> D[事务管理]
        D --> E[异常处理]
    ```
    
    **实现标准**：
    ```java
    // Service接口标准
    public interface UserService {
        UserResponse createUser(CreateUserRequest request);
        UserResponse getUserById(String id);
        List<UserResponse> getActiveUsers();
    }
    
    // Service实现标准
    @Service
    @Transactional(readOnly = true)
    public class UserServiceImpl implements UserService {
        
        private final UserRepository userRepository;
        
        public UserServiceImpl(UserRepository userRepository) {
            this.userRepository = userRepository;
        }
        
        @Transactional
        public UserResponse createUser(CreateUserRequest request) {
            if (StrUtil.isBlank(request.name())) {
                throw new BusinessException(ExceptionEnum.USERNAME_OR_PASSWORD_IS_NULL);
            }
            
            User user = User.builder()
                .name(request.name())
                .email(request.email())
                .status(UserStatus.ACTIVE)
                .createdAt(LocalDateTime.now())
                .build();
                
            return UserResponse.fromEntity(userRepository.save(user));
        }
    }
    ```
    
    ### Phase 4: 控制层实现 (20分钟)
    ```mermaid
    flowchart TD
        A[Controller设计] --> B[参数校验]
        B --> C[Service调用]
        C --> D[响应封装]
        D --> E[异常处理]
    ```
    
    **实现标准**：
    ```java
    @RestController
    @RequestMapping("/api/users")
    @Validated
    public class UserController {
        
        private final UserService userService;
        
        public UserController(UserService userService) {
            this.userService = userService;
        }
        
        @PostMapping
        public ResponseEntity<ResponseBean<UserResponse>> createUser(
                @Valid @RequestBody CreateUserRequest request) {
            UserResponse user = userService.createUser(request);
            return ResponseEntity.status(HttpStatus.CREATED)
                .body(ResponseBean.success(user));
        }
        
        @GetMapping("/{id}")
        public ResponseEntity<ResponseBean<UserResponse>> getUserById(
                @PathVariable String id) {
            UserResponse user = userService.getUserById(id);
            return ResponseEntity.ok(ResponseBean.success(user));
        }
    }
    ```
    
    ### Phase 5: 质量保证 (15分钟)
    ```mermaid
    flowchart LR
        A[代码规范检查] --> B[性能检查]
        B --> C[异常处理检查]
        C --> D[集成测试]
    ```
    
    **检查清单**：
    - [ ] 是否使用构造函数注入？
    - [ ] 是否使用LocalDateTime处理时间？
    - [ ] 是否使用Optional和Hutool工具类？
    - [ ] 是否使用Stream API？
    - [ ] 是否避免了N+1查询？
    - [ ] 是否遵循四层架构？
    - [ ] 命名是否清晰？
    - [ ] 是否有重复代码？
  </process>

  <criteria>
    ## 代码质量评价标准
    
    ### 规范遵循度 (40分)
    - ✅ 构造函数依赖注入 (10分)
    - ✅ LocalDateTime时间处理 (10分)
    - ✅ Optional和Hutool工具类使用 (10分)
    - ✅ Stream API集合处理 (10分)
    
    ### 架构设计 (30分)
    - ✅ 四层架构职责清晰 (15分)
    - ✅ RESTful API设计规范 (15分)
    
    ### 代码质量 (20分)
    - ✅ 命名清晰准确 (10分)
    - ✅ 无重复代码 (10分)
    
    ### 性能优化 (10分)
    - ✅ 避免N+1查询 (5分)
    - ✅ 合理使用缓存 (5分)
    
    **总分90分以上为优秀，80分以上为良好，70分以上为合格**
  </criteria>
</execution>
