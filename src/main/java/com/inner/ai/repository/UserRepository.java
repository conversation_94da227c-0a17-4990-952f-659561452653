package com.inner.ai.repository;

import com.inner.ai.entity.User;
import com.inner.ai.enums.UserStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 用户数据访问层
 */
@Repository
public interface UserRepository extends JpaRepository<User, Long>, JpaSpecificationExecutor<User> {
    
    /**
     * 根据邮箱查找用户
     */
    Optional<User> findByEmail(String email);
    
    /**
     * 根据状态查找用户列表
     */
    List<User> findByStatus(UserStatus status);
    
    /**
     * 根据邮箱和状态查找用户
     */
    Optional<User> findByEmailAndStatus(String email, UserStatus status);
    
    /**
     * 检查邮箱是否存在（排除指定ID）
     */
    @Query("SELECT COUNT(u) > 0 FROM User u WHERE u.email = :email AND u.id != :excludeId")
    boolean existsByEmailAndIdNot(@Param("email") String email, @Param("excludeId") Long excludeId);
    
    /**
     * 根据用户名模糊查询
     */
    List<User> findByNameContainingIgnoreCase(String name);
}
