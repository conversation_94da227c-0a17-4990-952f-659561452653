package com.inner.ai.controller;

import com.inner.ai.common.ApiResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 健康检查控制器
 */
@Tag(name = "系统监控", description = "系统健康检查相关接口")
@Slf4j
@RestController
@RequestMapping("/health")
public class HealthController {

    /**
     * 健康检查接口
     */
    @Operation(summary = "系统健康检查", description = "检查系统运行状态，返回系统基本信息")
    @GetMapping
    public ApiResponse<Map<String, Object>> health() {
        log.info("健康检查请求");

        Map<String, Object> healthInfo = new HashMap<>();
        healthInfo.put("status", "UP");
        healthInfo.put("timestamp", LocalDateTime.now());
        healthInfo.put("application", "inner-ai");
        healthInfo.put("version", "1.0.0");

        return ApiResponse.success("系统运行正常", healthInfo);
    }
}
