package com.inner.ai.controller;

import com.inner.ai.common.ApiResponse;
import com.inner.ai.dto.request.UserPageRequest;
import com.inner.ai.dto.request.UserRequest;
import com.inner.ai.dto.response.UserResponse;
import com.inner.ai.enums.UserStatus;
import com.inner.ai.service.UserService;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Min;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 用户控制器
 */
@Slf4j
@RestController
@RequestMapping("/users")
public class UserController {

    private final UserService userService;

    public UserController(UserService userService) {
        this.userService = userService;
    }


    @GetMapping("/vaild/{id}")
    public ApiResponse<Void> testValid(@PathVariable("id") @Min(value = 1, message = "用户ID必须大于0") Long id) {
        return ApiResponse.success();
    }

    @PostMapping("/vaild")
    public ApiResponse<Void> testValidPost(@RequestBody @Valid UserRequest request) {
        return ApiResponse.success();
    }

    /**
     * 创建用户
     */
    @PostMapping
    public ApiResponse<UserResponse> createUser(
            @Validated(UserRequest.Create.class) @RequestBody UserRequest request) {
        log.info("接收创建用户请求: {}", request.getEmail());

        UserResponse user = userService.createUser(request);
        return ApiResponse.success("用户创建成功", user);
    }

    /**
     * 根据ID获取用户
     */
    @GetMapping("/{id}")
    public ApiResponse<UserResponse> getUserById(
            @PathVariable @Min(value = 1, message = "用户ID必须大于0") Long id) {
        log.info("接收获取用户请求，ID: {}", id);

        UserResponse user = userService.getUserById(id);
        return ApiResponse.success(user);
    }

    /**
     * 更新用户信息
     */
    @PutMapping("/{id}")
    public ApiResponse<UserResponse> updateUser(
            @PathVariable @Min(value = 1, message = "用户ID必须大于0") Long id,
            @Validated(UserRequest.Update.class) @RequestBody UserRequest request) {
        log.info("接收更新用户请求，ID: {}", id);

        UserResponse user = userService.updateUser(id, request);
        return ApiResponse.success("用户更新成功", user);
    }

    /**
     * 删除用户
     */
    @DeleteMapping("/{id}")
    public ApiResponse<Void> deleteUser(
            @PathVariable @Min(value = 1, message = "用户ID必须大于0") Long id) {
        log.info("接收删除用户请求，ID: {}", id);

        userService.deleteUser(id);
        return ApiResponse.success("用户删除成功");
    }
    
    /**
     * 分页查询用户
     */
    @GetMapping
    public ApiResponse<Page<UserResponse>> getUserPage(
            @Validated(UserPageRequest.Query.class) UserPageRequest request) {
        log.info("接收分页查询用户请求，页码: {}, 大小: {}", request.getPage(), request.getSize());

        Page<UserResponse> userPage = userService.getUserPage(request);
        return ApiResponse.success(userPage);
    }

    /**
     * 根据状态获取用户列表
     */
    @GetMapping("/status/{status}")
    public ApiResponse<List<UserResponse>> getUsersByStatus(
            @PathVariable UserStatus status) {
        log.info("接收根据状态查询用户请求，状态: {}", status);

        List<UserResponse> users = userService.getUsersByStatus(status);
        return ApiResponse.success(users);
    }

    /**
     * 根据邮箱获取用户
     */
    @GetMapping("/email/{email}")
    public ApiResponse<UserResponse> getUserByEmail(@PathVariable String email) {
        log.info("接收根据邮箱查询用户请求，邮箱: {}", email);

        UserResponse user = userService.getUserByEmail(email);
        return ApiResponse.success(user);
    }

    /**
     * 检查邮箱是否存在
     */
    @GetMapping("/email/{email}/exists")
    public ApiResponse<Boolean> checkEmailExists(@PathVariable String email) {
        log.info("接收检查邮箱是否存在请求，邮箱: {}", email);

        boolean exists = userService.existsByEmail(email);
        return ApiResponse.success(exists);
    }

    /**
     * 激活用户
     */
    @PatchMapping("/{id}/activate")
    public ApiResponse<UserResponse> activateUser(
            @PathVariable @Min(value = 1, message = "用户ID必须大于0") Long id) {
        log.info("接收激活用户请求，ID: {}", id);

        UserResponse user = userService.activateUser(id);
        return ApiResponse.success("用户激活成功", user);
    }

    /**
     * 锁定用户
     */
    @PatchMapping("/{id}/lock")
    public ApiResponse<UserResponse> lockUser(
            @PathVariable @Min(value = 1, message = "用户ID必须大于0") Long id) {
        log.info("接收锁定用户请求，ID: {}", id);

        UserResponse user = userService.lockUser(id);
        return ApiResponse.success("用户锁定成功", user);
    }
}
