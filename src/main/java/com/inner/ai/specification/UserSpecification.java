package com.inner.ai.specification;

import cn.hutool.core.util.StrUtil;
import com.inner.ai.entity.User;
import com.inner.ai.enums.UserStatus;
import jakarta.persistence.criteria.Predicate;
import org.springframework.data.jpa.domain.Specification;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 用户查询规格工具类
 */
public class UserSpecification {
    
    /**
     * 根据用户名模糊查询
     */
    public static Specification<User> nameLike(String name) {
        return (root, query, criteriaBuilder) -> {
            if (StrUtil.isBlank(name)) {
                return criteriaBuilder.conjunction();
            }
            return criteriaBuilder.like(
                    criteriaBuilder.lower(root.get("name")),
                    "%" + name.toLowerCase() + "%"
            );
        };
    }
    
    /**
     * 根据邮箱精确查询
     */
    public static Specification<User> emailEquals(String email) {
        return (root, query, criteriaBuilder) -> {
            if (StrUtil.isBlank(email)) {
                return criteriaBuilder.conjunction();
            }
            return criteriaBuilder.equal(root.get("email"), email);
        };
    }
    
    /**
     * 根据手机号精确查询
     */
    public static Specification<User> phoneEquals(String phone) {
        return (root, query, criteriaBuilder) -> {
            if (StrUtil.isBlank(phone)) {
                return criteriaBuilder.conjunction();
            }
            return criteriaBuilder.equal(root.get("phone"), phone);
        };
    }
    
    /**
     * 根据状态查询
     */
    public static Specification<User> statusEquals(UserStatus status) {
        return (root, query, criteriaBuilder) -> {
            if (status == null) {
                return criteriaBuilder.conjunction();
            }
            return criteriaBuilder.equal(root.get("status"), status);
        };
    }
    
    /**
     * 根据状态列表查询
     */
    public static Specification<User> statusIn(List<UserStatus> statuses) {
        return (root, query, criteriaBuilder) -> {
            if (statuses == null || statuses.isEmpty()) {
                return criteriaBuilder.conjunction();
            }
            return root.get("status").in(statuses);
        };
    }
    
    /**
     * 创建时间范围查询
     */
    public static Specification<User> createdAtBetween(LocalDateTime startTime, LocalDateTime endTime) {
        return (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();
            
            if (startTime != null) {
                predicates.add(criteriaBuilder.greaterThanOrEqualTo(root.get("createdAt"), startTime));
            }
            
            if (endTime != null) {
                predicates.add(criteriaBuilder.lessThanOrEqualTo(root.get("createdAt"), endTime));
            }
            
            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
    }
    
    /**
     * 排除已删除用户
     */
    public static Specification<User> notDeleted() {
        return (root, query, criteriaBuilder) -> 
                criteriaBuilder.notEqual(root.get("status"), UserStatus.DELETED);
    }
    
    /**
     * 只查询激活用户
     */
    public static Specification<User> onlyActive() {
        return (root, query, criteriaBuilder) -> 
                criteriaBuilder.equal(root.get("status"), UserStatus.ACTIVE);
    }
    
    /**
     * 组合查询条件
     */
    public static Specification<User> buildSpecification(String name, String email, 
                                                        String phone, UserStatus status,
                                                        LocalDateTime startTime, LocalDateTime endTime,
                                                        boolean excludeDeleted) {
        return Specification.where(nameLike(name))
                .and(emailEquals(email))
                .and(phoneEquals(phone))
                .and(statusEquals(status))
                .and(createdAtBetween(startTime, endTime))
                .and(excludeDeleted ? notDeleted() : null);
    }
}
