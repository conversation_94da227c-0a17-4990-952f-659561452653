package com.inner.ai.exception;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import com.inner.ai.common.ApiResponse;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.BindException;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.validation.ObjectError;
import org.springframework.validation.method.ParameterValidationResult;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.annotation.HandlerMethodValidationException;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import org.springframework.web.servlet.NoHandlerFoundException;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 全局异常处理器
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    /**
     * 处理业务异常
     */
    @ExceptionHandler(BusinessException.class)
    public ApiResponse<?> handleBusinessException(BusinessException e) {
        log.error("BusinessException: {}", e.getMessage(), e);

        String message = e.getMessage();
        // 如果有消息参数，进行格式化
        if (ArrayUtil.isNotEmpty(e.getMessageParams())) {
            message = String.format(message, (Object[]) e.getMessageParams());
        }

        return ApiResponse.failed(e.getCode(), message, e.getErrorData());
    }
    
    /**
     * 处理参数校验异常（@Valid）
     */
//    @ExceptionHandler({MethodArgumentNotValidException.class})
//    public ApiResponse<?> handleMethodArgumentNotValidException(
//            MethodArgumentNotValidException e) {
//
//        log.error("MethodArgumentNotValidException: {}", e.getMessage());
//
//        String message = e.getBindingResult().getFieldErrors()
//                .stream()
//                .map(FieldError::getDefaultMessage)
//                .collect(Collectors.joining("; "));
//
//        return ApiResponse.failed(HttpStatus.BAD_REQUEST.value(),
//                StrUtil.isNotBlank(message) ? message : "参数校验失败");
//    }

    @ExceptionHandler(value = MethodArgumentNotValidException.class)
    public ApiResponse<?> methodArgumentNotValidException(MethodArgumentNotValidException exception) {
        BindingResult result = exception.getBindingResult();
        StringBuilder errorMsg = new StringBuilder();
        if (result.hasErrors()) {
            List<FieldError> fieldErrors = result.getFieldErrors();
            fieldErrors.forEach(error -> {
                log.error(error.getField() + ":" + error.getDefaultMessage(), exception);
                errorMsg.append(error.getField()).append(":").append(error.getDefaultMessage());
            });
        }
//        return ResponseBean.failed(HttpStatus.BAD_REQUEST.value(), errorMsg.toString());
                return ApiResponse.failed(HttpStatus.BAD_REQUEST.value(),
                StrUtil.isNotBlank(errorMsg.toString()) ? errorMsg.toString() : "参数校验失败");
    }

    @ExceptionHandler(HandlerMethodValidationException.class)
    public ApiResponse<?> handleHandlerMethodValidation(HandlerMethodValidationException ex) {
        log.warn("HandlerMethodValidationException: {}", ex.getMessage());
        List<String> errors = ex.getAllValidationResults().stream()
                .flatMap(result -> result.getResolvableErrors().stream())
                .map(error -> {
                    // 获取参数名（Spring 6.1+ 支持）
                    String paramName = error.getDefaultMessage();

                    return paramName + ": " + error.getDefaultMessage();
                })
                .toList();
        log.error(errors.toString());
        return ApiResponse.failed(HttpStatus.BAD_REQUEST.value(),
                StrUtil.isNotBlank("aa") ? "aa" : "参数校验失败");
    }


    /**
     * 处理绑定异常
     */
    @ExceptionHandler(BindException.class)
    public ApiResponse<?> handleBindException(BindException e) {
        log.error("BindException: {}", e.getMessage());

        String message = e.getBindingResult().getFieldErrors()
                .stream()
                .map(FieldError::getDefaultMessage)
                .collect(Collectors.joining("; "));

        return ApiResponse.failed(HttpStatus.BAD_REQUEST.value(),
                StrUtil.isNotBlank(message) ? message : "参数绑定失败");
    }

    /**
     * 处理约束违反异常（@Validated）
     */
    @ExceptionHandler({ConstraintViolationException.class})
    public ApiResponse<?> handleConstraintViolationException(
            ConstraintViolationException e) {
        log.error("ConstraintViolationException: {}", e.getMessage());

        String message = e.getConstraintViolations()
                .stream()
                .map(ConstraintViolation::getMessage)
                .collect(Collectors.joining("; "));

        return ApiResponse.failed(HttpStatus.BAD_REQUEST.value(),
                StrUtil.isNotBlank(message) ? message : "参数约束违反");
    }
    
    /**
     * 处理缺少请求参数异常
     */
    @ExceptionHandler(MissingServletRequestParameterException.class)
    public ApiResponse<?> handleMissingServletRequestParameterException(
            MissingServletRequestParameterException e) {
        log.error("MissingServletRequestParameterException: {}", e.getMessage());

        String message = String.format("缺少必需的请求参数: %s", e.getParameterName());
        return ApiResponse.failed(HttpStatus.BAD_REQUEST.value(), message);
    }

    /**
     * 处理方法参数类型不匹配异常
     */
    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    public ApiResponse<?> handleMethodArgumentTypeMismatchException(
            MethodArgumentTypeMismatchException e) {
        log.error("MethodArgumentTypeMismatchException: {}", e.getMessage());

        String message = String.format("参数 %s 的值 %s 类型不正确", e.getName(), e.getValue());
        return ApiResponse.failed(HttpStatus.BAD_REQUEST.value(), message);
    }

    /**
     * 处理HTTP消息不可读异常
     */
    @ExceptionHandler(HttpMessageNotReadableException.class)
    public ApiResponse<?> handleHttpMessageNotReadableException(
            HttpMessageNotReadableException e) {
        log.error("HttpMessageNotReadableException: {}", e.getMessage());

        return ApiResponse.failed(HttpStatus.BAD_REQUEST.value(), "请求体格式错误");
    }

    /**
     * 处理HTTP请求方法不支持异常
     */
    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    public ApiResponse<?> handleHttpRequestMethodNotSupportedException(
            HttpRequestMethodNotSupportedException e) {
        log.error("HttpRequestMethodNotSupportedException: {}", e.getMessage());

        String message = String.format("不支持的请求方法: %s", e.getMethod());
        return ApiResponse.failed(HttpStatus.METHOD_NOT_ALLOWED.value(), message);
    }

    /**
     * 处理404异常
     */
    @ExceptionHandler(NoHandlerFoundException.class)
    public ApiResponse<?> handleNoHandlerFoundException(
            NoHandlerFoundException e) {
        log.error("NoHandlerFoundException: {}", e.getMessage());

        String message = String.format("请求的资源不存在: %s %s", e.getHttpMethod(), e.getRequestURL());
        return ApiResponse.failed(HttpStatus.NOT_FOUND.value(), message);
    }

    /**
     * 处理其他未知异常
     */
    @ExceptionHandler(Exception.class)
    public ApiResponse<?> handleException(Exception e) {
        log.error("SystemException: {}", e.getMessage(), e);

        return ApiResponse.failed(HttpStatus.INTERNAL_SERVER_ERROR.value(),
                "系统内部错误，请稍后重试");
    }
}
