package com.inner.ai.exception;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import com.inner.ai.common.ApiResponse;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.BindException;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.validation.ObjectError;
import org.springframework.validation.method.ParameterValidationResult;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.annotation.HandlerMethodValidationException;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import org.springframework.web.servlet.NoHandlerFoundException;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 全局异常处理器
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    /**
     * 处理业务异常
     */
    @ExceptionHandler(BusinessException.class)
    public ApiResponse<?> handleBusinessException(BusinessException e) {
        log.error("BusinessException: {}", e.getMessage(), e);

        String message = e.getMessage();
        // 如果有消息参数，进行格式化
        if (ArrayUtil.isNotEmpty(e.getMessageParams())) {
            message = String.format(message, (Object[]) e.getMessageParams());
        }

        return ApiResponse.failed(e.getCode(), message, e.getErrorData());
    }
    
    /**
     * 处理请求体参数校验异常（@Valid @RequestBody）
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ApiResponse<Object> handleMethodArgumentNotValidException(MethodArgumentNotValidException ex) {
        log.warn("请求体参数校验异常: {}", ex.getMessage());

        List<String> errors = buildFieldErrors(ex.getBindingResult());
        String errorMessage = String.join("; ", errors);

        return ApiResponse.failed(HttpStatus.BAD_REQUEST.value(),
                StrUtil.isNotBlank(errorMessage) ? errorMessage : "请求体参数校验失败");
    }

    /**
     * 处理方法级别参数校验异常（Spring Boot 3.x）
     * 主要处理@PathVariable、@RequestParam等参数校验失败
     */
    @ExceptionHandler(HandlerMethodValidationException.class)
    public ApiResponse<Object> handleHandlerMethodValidationException(HandlerMethodValidationException ex) {
        log.warn("方法级别参数校验异常: {}", ex.getMessage());

        List<String> errors = buildMethodParameterErrors(ex);
        String errorMessage = String.join("; ", errors);

        return ApiResponse.failed(HttpStatus.BAD_REQUEST.value(),
                StrUtil.isNotBlank(errorMessage) ? errorMessage : "方法参数校验失败");
    }


    /**
     * 处理绑定异常
     */
    @ExceptionHandler(BindException.class)
    public ApiResponse<?> handleBindException(BindException e) {
        log.error("BindException: {}", e.getMessage());

        String message = e.getBindingResult().getFieldErrors()
                .stream()
                .map(FieldError::getDefaultMessage)
                .collect(Collectors.joining("; "));

        return ApiResponse.failed(HttpStatus.BAD_REQUEST.value(),
                StrUtil.isNotBlank(message) ? message : "参数绑定失败");
    }

    /**
     * 处理约束违反异常（@Validated）
     */
    @ExceptionHandler(ConstraintViolationException.class)
    public ApiResponse<Object> handleConstraintViolationException(ConstraintViolationException ex) {
        log.warn("约束违反异常: {}", ex.getMessage());

        List<String> errors = buildConstraintViolationErrors(ex);
        String errorMessage = String.join("; ", errors);

        return ApiResponse.failed(HttpStatus.BAD_REQUEST.value(),
                StrUtil.isNotBlank(errorMessage) ? errorMessage : "约束校验失败");
    }
    
    /**
     * 处理缺少请求参数异常
     */
    @ExceptionHandler(MissingServletRequestParameterException.class)
    public ApiResponse<?> handleMissingServletRequestParameterException(
            MissingServletRequestParameterException e) {
        log.error("MissingServletRequestParameterException: {}", e.getMessage());

        String message = String.format("缺少必需的请求参数: %s", e.getParameterName());
        return ApiResponse.failed(HttpStatus.BAD_REQUEST.value(), message);
    }

    /**
     * 处理方法参数类型不匹配异常
     */
    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    public ApiResponse<?> handleMethodArgumentTypeMismatchException(
            MethodArgumentTypeMismatchException e) {
        log.error("MethodArgumentTypeMismatchException: {}", e.getMessage());

        String message = String.format("参数 %s 的值 %s 类型不正确", e.getName(), e.getValue());
        return ApiResponse.failed(HttpStatus.BAD_REQUEST.value(), message);
    }

    /**
     * 处理HTTP消息不可读异常
     */
    @ExceptionHandler(HttpMessageNotReadableException.class)
    public ApiResponse<?> handleHttpMessageNotReadableException(
            HttpMessageNotReadableException e) {
        log.error("HttpMessageNotReadableException: {}", e.getMessage());

        return ApiResponse.failed(HttpStatus.BAD_REQUEST.value(), "请求体格式错误");
    }

    /**
     * 处理HTTP请求方法不支持异常
     */
    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    public ApiResponse<?> handleHttpRequestMethodNotSupportedException(
            HttpRequestMethodNotSupportedException e) {
        log.error("HttpRequestMethodNotSupportedException: {}", e.getMessage());

        String message = String.format("不支持的请求方法: %s", e.getMethod());
        return ApiResponse.failed(HttpStatus.METHOD_NOT_ALLOWED.value(), message);
    }

    /**
     * 处理404异常
     */
    @ExceptionHandler(NoHandlerFoundException.class)
    public ApiResponse<?> handleNoHandlerFoundException(
            NoHandlerFoundException e) {
        log.error("NoHandlerFoundException: {}", e.getMessage());

        String message = String.format("请求的资源不存在: %s %s", e.getHttpMethod(), e.getRequestURL());
        return ApiResponse.failed(HttpStatus.NOT_FOUND.value(), message);
    }

    /**
     * 处理其他未知异常
     */
    @ExceptionHandler(Exception.class)
    public ApiResponse<?> handleException(Exception e) {
        log.error("SystemException: {}", e.getMessage(), e);

        return ApiResponse.failed(HttpStatus.INTERNAL_SERVER_ERROR.value(),
                "系统内部错误，请稍后重试");
    }

    /**
     * 构建请求体字段校验错误信息
     */
    private List<String> buildFieldErrors(BindingResult bindingResult) {
        List<String> errors = new ArrayList<>();

        // 处理字段错误
        bindingResult.getFieldErrors().forEach(error -> {
            String fieldName = error.getField();
            String errorMessage = error.getDefaultMessage();

            // 处理嵌套对象字段，如 user.name -> 对象 user 的字段 name
            if (fieldName.contains(".")) {
                String[] parts = fieldName.split("\\.");
                String objectName = parts[0];
                String propertyName = String.join(".", Arrays.copyOfRange(parts, 1, parts.length));
                errors.add(String.format("对象 %s 的字段 %s: %s", objectName, propertyName, errorMessage));
            } else {
                errors.add(String.format("字段 %s: %s", fieldName, errorMessage));
            }
        });

        // 处理对象级别错误
        bindingResult.getGlobalErrors().forEach(error -> {
            String objectName = error.getObjectName();
            String errorMessage = error.getDefaultMessage();
            errors.add(String.format("对象 %s: %s", objectName, errorMessage));
        });

        return errors;
    }

    /**
     * 构建方法参数校验错误信息
     */
    private List<String> buildMethodParameterErrors(HandlerMethodValidationException ex) {
        return ex.getAllValidationResults().stream()
                .map(this::buildSingleParameterError)
                .filter(StrUtil::isNotBlank)
                .toList();
    }

    /**
     * 构建单个参数的错误信息
     */
    private String buildSingleParameterError(ParameterValidationResult result) {
        String parameterName = getParameterDisplayName(result);
        String parameterType = result.getMethodParameter().getParameterType().getSimpleName();

        // 获取所有错误信息
        List<String> errorMessages = result.getResolvableErrors().stream()
                .map(error -> error.getDefaultMessage())
                .filter(StrUtil::isNotBlank)
                .distinct()
                .toList();

        if (errorMessages.isEmpty()) {
            return String.format("参数 %s(%s) 校验失败", parameterName, parameterType);
        }

        return String.format("参数 %s(%s): %s",
                parameterName, parameterType, String.join(", ", errorMessages));
    }

    /**
     * 获取参数的显示名称
     */
    private String getParameterDisplayName(ParameterValidationResult result) {
        String parameterName = result.getMethodParameter().getParameterName();

        // 如果参数名可用且不是编译器生成的默认名称，直接使用
        if (StrUtil.isNotBlank(parameterName) && !isCompilerGeneratedName(parameterName)) {
            return parameterName;
        }

        // 否则根据上下文推断一个合理的名称
        return generateParameterDisplayName(result);
    }

    /**
     * 判断是否为编译器生成的默认参数名
     */
    private boolean isCompilerGeneratedName(String parameterName) {
        return parameterName.matches("arg\\d+") || "param".equals(parameterName);
    }

    /**
     * 根据上下文生成参数显示名称
     */
    private String generateParameterDisplayName(ParameterValidationResult result) {
        String parameterType = result.getMethodParameter().getParameterType().getSimpleName();
        int parameterIndex = result.getMethodParameter().getParameterIndex();

        // 根据参数类型的语义推断
        return switch (parameterType.toLowerCase()) {
            case "long", "integer" -> parameterIndex == 0 ? "id" : "number" + (parameterIndex + 1);
            case "string" -> parameterIndex == 0 ? "name" : "text" + (parameterIndex + 1);
            case "boolean" -> "flag" + (parameterIndex + 1);
            default -> "param" + (parameterIndex + 1);
        };
    }

    /**
     * 构建约束违反错误信息
     */
    private List<String> buildConstraintViolationErrors(ConstraintViolationException ex) {
        return ex.getConstraintViolations().stream()
                .map(violation -> {
                    String propertyPath = violation.getPropertyPath().toString();
                    String errorMessage = violation.getMessage();

                    // 处理嵌套属性路径，如 user.name -> 对象 user 的属性 name
                    if (propertyPath.contains(".")) {
                        String[] parts = propertyPath.split("\\.");
                        if (parts.length >= 2) {
                            String objectName = parts[parts.length - 2];
                            String propertyName = parts[parts.length - 1];
                            return String.format("对象 %s 的属性 %s: %s", objectName, propertyName, errorMessage);
                        }
                    }

                    return String.format("属性 %s: %s", propertyPath, errorMessage);
                })
                .toList();
    }


}
