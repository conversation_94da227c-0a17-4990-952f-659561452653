package com.inner.ai.exception;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import com.inner.ai.common.ApiResponse;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.BindException;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.validation.ObjectError;
import org.springframework.validation.method.ParameterValidationResult;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import org.springframework.web.servlet.NoHandlerFoundException;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 全局异常处理器
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    /**
     * 处理业务异常
     */
    @ExceptionHandler(BusinessException.class)
    public ApiResponse<?> handleBusinessException(BusinessException e) {
        log.error("BusinessException: {}", e.getMessage(), e);

        String message = e.getMessage();
        // 如果有消息参数，进行格式化
        if (ArrayUtil.isNotEmpty(e.getMessageParams())) {
            message = String.format(message, (Object[]) e.getMessageParams());
        }

        return ApiResponse.failed(e.getCode(), message, e.getErrorData());
    }

    /**
     * 处理请求体参数校验异常（@Valid @RequestBody）
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ApiResponse<Object> handleMethodArgumentNotValidException(MethodArgumentNotValidException ex) {
        log.warn("请求体参数校验异常: {}", ex.getMessage());

        List<String> errors = buildFieldErrors(ex.getBindingResult());
        String errorMessage = String.join("; ", errors);

        return ApiResponse.failed(HttpStatus.BAD_REQUEST.value(),
                StrUtil.isNotBlank(errorMessage) ? errorMessage : "请求体参数校验失败");
    }


    /**
     * 处理约束违反异常（@Validated）
     */
    @ExceptionHandler(ConstraintViolationException.class)
    public ApiResponse<Object> handleConstraintViolationException(ConstraintViolationException ex) {
        log.warn("约束违反异常: {}", ex.getMessage());

        List<String> errors = buildConstraintViolationErrors(ex);
        String errorMessage = String.join("; ", errors);

        return ApiResponse.failed(HttpStatus.BAD_REQUEST.value(),
                StrUtil.isNotBlank(errorMessage) ? errorMessage : "约束校验失败");
    }
    



    /**
     * 处理其他未知异常
     */
    @ExceptionHandler(Exception.class)
    public ApiResponse<?> handleException(Exception e) {
        log.error("SystemException: {}", e.getMessage(), e);

        return ApiResponse.failed(HttpStatus.INTERNAL_SERVER_ERROR.value(),
                "系统内部错误，请稍后重试");
    }

    /**
     * 构建请求体字段校验错误信息
     */
    private List<String> buildFieldErrors(BindingResult bindingResult) {
        List<String> errors = new ArrayList<>();

        // 处理字段级别错误
        errors.addAll(buildFieldLevelErrors(bindingResult.getFieldErrors()));

        // 处理对象级别错误
        errors.addAll(buildObjectLevelErrors(bindingResult.getGlobalErrors()));

        return errors;
    }

    /**
     * 构建字段级别错误信息
     */
    private List<String> buildFieldLevelErrors(List<FieldError> fieldErrors) {
        return fieldErrors.stream()
                .filter(error -> StrUtil.isNotBlank(error.getDefaultMessage()))
                .map(this::formatFieldError)
                .toList();
    }

    /**
     * 格式化单个字段错误
     */
    private String formatFieldError(FieldError error) {
        String fieldName = error.getField();
        String errorMessage = error.getDefaultMessage();

        // 处理嵌套对象字段，如 user.name -> 对象 user 的字段 name
        if (fieldName.contains(".")) {
            return formatNestedFieldError(fieldName, errorMessage);
        }

        return String.format("字段 %s: %s", fieldName, errorMessage);
    }

    /**
     * 格式化嵌套字段错误
     */
    private String formatNestedFieldError(String fieldName, String errorMessage) {
        String[] parts = fieldName.split("\\.");
        if (parts.length < 2) {
            return String.format("字段 %s: %s", fieldName, errorMessage);
        }

        String objectName = parts[0];
        String propertyPath = String.join(".", Arrays.copyOfRange(parts, 1, parts.length));

        return String.format("对象 %s 的字段 %s: %s", objectName, propertyPath, errorMessage);
    }

    /**
     * 构建对象级别错误信息
     */
    private List<String> buildObjectLevelErrors(List<ObjectError> globalErrors) {
        return globalErrors.stream()
                .filter(error -> StrUtil.isNotBlank(error.getDefaultMessage()))
                .map(error -> String.format("对象 %s: %s", error.getObjectName(), error.getDefaultMessage()))
                .toList();
    }



    /**
     * 构建约束违反错误信息
     */
    private List<String> buildConstraintViolationErrors(ConstraintViolationException ex) {
        return ex.getConstraintViolations().stream()
                .filter(violation -> StrUtil.isNotBlank(violation.getMessage()))
                .map(this::formatConstraintViolation)
                .distinct()
                .toList();
    }

    /**
     * 格式化约束违反错误
     */
    private String formatConstraintViolation(ConstraintViolation<?> violation) {
        String propertyPath = violation.getPropertyPath().toString();
        String errorMessage = violation.getMessage();

        // 处理嵌套属性路径
        if (propertyPath.contains(".")) {
            return formatNestedPropertyError(propertyPath, errorMessage);
        }

        return String.format("属性 %s: %s", propertyPath, errorMessage);
    }

    /**
     * 格式化嵌套属性错误
     */
    private String formatNestedPropertyError(String propertyPath, String errorMessage) {
        String[] parts = propertyPath.split("\\.");
        if (parts.length < 2) {
            return String.format("属性 %s: %s", propertyPath, errorMessage);
        }

        // 获取对象名和属性名
        String objectName = parts[parts.length - 2];
        String propertyName = parts[parts.length - 1];

        return String.format("对象 %s 的属性 %s: %s", objectName, propertyName, errorMessage);
    }


}
