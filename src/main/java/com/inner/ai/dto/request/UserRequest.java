package com.inner.ai.dto.request;

import com.inner.ai.enums.UserStatus;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 用户请求DTO（统一创建和更新）
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserRequest {
    
    /**
     * 校验分组接口
     */
    public interface Create {}
    public interface Update {}
    
    /**
     * 用户名
     */
    @NotBlank(message = "用户名不能为空")
    @Size(min = 2, max = 50, message = "用户名长度必须在2-50个字符之间", groups = {Create.class, Update.class})
    private String name;
    
    /**
     * 邮箱
     */
    @NotBlank(message = "邮箱不能为空", groups = {Create.class})
    @Email(message = "邮箱格式不正确", groups = {Create.class, Update.class})
    @Size(max = 100, message = "邮箱长度不能超过100个字符", groups = {Create.class, Update.class})
    private String email;
    
    /**
     * 手机号
     */
    @NotBlank(message = "手机号不能为空", groups = {Create.class})
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确", groups = {Create.class, Update.class})
    private String phone;

    @NotEmpty(message = "auts不能为空", groups = {Create.class})
    private List<@NotBlank(message = "集合字符串不能为空", groups = {Create.class}) String> auts;


    @Valid
    @NotEmpty(message = "userPageRequests不能为空", groups = {Create.class})
    private List<@NotNull(message = "userPageRequests集合不能为空", groups = {Create.class}) UserPageRequest> userPageRequests;
    
    /**
     * 用户状态
     * 创建时默认为ACTIVE，更新时可选
     */
    private UserStatus status = UserStatus.ACTIVE;
}
