package com.inner.ai.dto.request;

import com.inner.ai.enums.UserStatus;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 用户分页查询请求DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserPageRequest {

    /**
     * 校验分组接口
     */
    public interface Query {}

    /**
     * 页码，从0开始
     */
    @Min(value = 0, message = "页码不能小于0", groups = {Query.class})
    private Integer page = 0;

    /**
     * 每页大小
     */
    @Min(value = 1, message = "每页大小不能小于1", groups = {Query.class})
    @Max(value = 100, message = "每页大小不能超过100", groups = {Query.class})
    private Integer size = 20;
    
    /**
     * 用户名（模糊查询）
     */
    @NotBlank(message = "name不能为空", groups = {UserRequest.Create.class})
    private String name;
    
    /**
     * 邮箱（精确查询）
     */
    private String email;
    
    /**
     * 用户状态
     */
    private UserStatus status;
    
    /**
     * 排序字段，默认按创建时间倒序
     */
    private String sortBy = "createdAt";
    
    /**
     * 排序方向，默认倒序
     */
    private String sortDirection = "desc";
}
