package com.inner.ai.entity;

import com.inner.ai.enums.UserStatus;
import jakarta.persistence.*;
import lombok.*;

/**
 * 用户实体类
 */
@Entity
@Table(name = "users")
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public class User extends EntityBase {
    
    /**
     * 用户名
     */
    @Column(nullable = false, length = 50)
    private String name;
    
    /**
     * 邮箱
     */
    @Column(nullable = false, unique = true, length = 100)
    private String email;
    
    /**
     * 手机号
     */
    @Column(length = 20)
    private String phone;
    
    /**
     * 用户状态
     */
    @Enumerated(EnumType.STRING)
    @Column(nullable = false, length = 20)
    private UserStatus status;
}
