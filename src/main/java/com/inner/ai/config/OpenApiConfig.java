package com.inner.ai.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * OpenAPI配置类
 */
@Configuration
public class OpenApiConfig {

    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title("Inner AI 系统API文档")
                        .description("基于Spring Boot 3.x + JPA + PostgreSQL的AI系统后端API")
                        .version("1.0.0")
                        .contact(new Contact()
                                .name("Inner AI Team")
                                .email("<EMAIL>")
                                .url("https://github.com/inner-ai"))
                        .license(new License()
                                .name("MIT License")
                                .url("https://opensource.org/licenses/MIT")));
    }
}
