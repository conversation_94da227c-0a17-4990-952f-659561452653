package com.inner.ai.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.domain.AuditorAware;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;

import java.util.Optional;

/**
 * JPA审计配置
 */
@Configuration
@EnableJpaAuditing(auditorAwareRef = "auditorProvider")
public class JpaAuditingConfig {
    
    /**
     * 审计员提供者
     * 在实际项目中，这里应该从SecurityContext或其他地方获取当前用户
     */
    @Bean
    public AuditorAware<String> auditorProvider() {
        return () -> {
            // TODO: 从SecurityContext获取当前用户
            // 暂时返回系统用户
            return Optional.of("system");
        };
    }
}
