package com.inner.ai.constants;

/**
 * 用户相关常量类
 */
public final class UserConstants {
    
    private UserConstants() {}

    /**
     * 用户名长度限制
     */
    public static final int USERNAME_MIN_LENGTH = 2;

    public static final int USERNAME_MAX_LENGTH = 20;

    /**
     * 密码长度限制
     */
    public static final int PASSWORD_MIN_LENGTH = 5;

    public static final int PASSWORD_MAX_LENGTH = 20;

    /**
     * redis login key
     */
    public static final String LOGIN_USER_KEY = "login:token:";

    /**
     * redis login key
     */
    public static final String TOKEN_PREFIX = "Bearer ";

    /**
     * redis login ttl
     */
    public static final Integer LOGIN_USER_TTL = 30;

    /**
     * 请求头信息
     */
    public static final String AUTHORIZATION = "authorization";

    /**
     * 认证授权未通过相应信息
     */
    public static final String CONTENT_TYPE = "text/html;charset=UTF-8";
    public static final String HTTP_UNAUTHORIZED_MESSAGE = "用户未登录或用户信息已过期，请重新登录！";

    /**
     * 拦截路径
     */
    public static final String INTERCEPT_PATH = "/**";
}
