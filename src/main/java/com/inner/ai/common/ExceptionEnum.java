package com.inner.ai.common;

import lombok.Getter;


@Getter
public enum ExceptionEnum {

    USERNAME_OR_PASSWORD_IS_NULL(001, "用户名或密码不能为空！"),
    PASSWORD_NOT_IN_SPECIFIED_RANGE(002, "用户密码不在指定范围！"),
    USERNAME_NOT_IN_SPECIFIED_RANGE(003, "用户名不在指定范围！"),
    USERNAME_NOT_EXIST(004, "登录用户: %s 不存在！"),
    USERNAME_DEACTIVATE(005, "登录用户: %s 已停用！"),
    USERNAME_OR_PASSWORD_INCORRECT(006, "用户名或密码不正确！错误第%s次，超过%s次将锁定账户%s分钟"),
    CURRENT_USERNAME_IS_NULL(007, "当前登录人为空！"),
    CIPHERTEXT_INCORRECT_FORMATTING(010, "密文格式不正确！"),
    CAPTCHA_IS_NULL(011, "验证码不能为空！"),
    CAPTCHA_IS_LAPSE(012, "验证码已失效！"),
    CAPTCHA_IS_ERROR(013, "验证码错误！"),
    USERNAME_LOCK(014, "密码输入错误超过%s次，帐户锁定%s分钟"),
    FILE_URL_IS_NULL(015, "文件路径为空"),
    DB_FILE_URL_IS_NULL(015, "文件路径不存在:%s"),
    FILE_IS_NULL(016, "上传文件为空"),
    EXCEL_TEMPLATE_ERROR(017, "模板对比错误, 请检查并重新上传"),

    // 用户相关异常
    USER_NOT_FOUND(1001, "用户不存在"),
    USER_EMAIL_EXISTS(1002, "邮箱已被注册"),
    USER_PHONE_EXISTS(1003, "手机号已被注册"),
    USER_STATUS_INVALID(1004, "用户状态无效"),
    USER_ALREADY_DELETED(1005, "用户已被删除"),
    USER_ALREADY_LOCKED(1006, "用户已被锁定"),
    USER_ALREADY_ACTIVE(1007, "用户已是激活状态"),
    EXCEL_FILE_CONTENT_EMPTY(020, "上传的excel文件内容为空"),
    FILE_CONTENT_VERIFICATION_FAILED(021, "文件内容校验不通过,请检查并重新上传");




    private final int code;

    private final String message;

    ExceptionEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }
}
