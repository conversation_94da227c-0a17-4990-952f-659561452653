

package com.inner.ai.common;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.http.HttpStatus;

import java.io.Serializable;


@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ApiResponse<T> implements Serializable {

    private int code;
    private String message;
    private T data;

    public static <T> ApiResponse success() {
        return ApiResponse.builder()
                .code(HttpStatus.OK.value())
                .message(HttpStatus.OK.getReasonPhrase())
                .build();
    }

    public static <T> ApiResponse success(String message) {
        return ApiResponse.builder()
                .code(HttpStatus.OK.value())
                .message(message)
                .build();
    }

    public static <T> ApiResponse success(T data) {
        return ApiResponse.builder()
                .code(HttpStatus.OK.value())
                .message(HttpStatus.OK.getReasonPhrase())
                .data(data)
                .build();
    }

    public static <T> ApiResponse success(String message, T data) {
        return ApiResponse.builder()
                .code(HttpStatus.OK.value())
                .message(message)
                .data(data)
                .build();
    }


    public static <T> ApiResponse failed() {
        return ApiResponse.builder()
                .code(HttpStatus.INTERNAL_SERVER_ERROR.value())
                .message(HttpStatus.INTERNAL_SERVER_ERROR.getReasonPhrase())
                .build();
    }

    public static <T> ApiResponse failed(String message) {
        return ApiResponse.builder()
                .code(HttpStatus.INTERNAL_SERVER_ERROR.value())
                .message(message)
                .build();
    }

    public static <T> ApiResponse failed(int code, String message) {
        return ApiResponse.builder()
                .code(code)
                .message(message)
                .build();
    }

    public static <T> ApiResponse failed(int code, String message, Object data) {
        return ApiResponse.builder()
                .code(code)
                .message(message)
                .data(data)
                .build();
    }



}

