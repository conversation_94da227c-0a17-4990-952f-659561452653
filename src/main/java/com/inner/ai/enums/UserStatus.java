package com.inner.ai.enums;

/**
 * 用户状态枚举
 */
public enum UserStatus {
    
    /**
     * 激活状态
     */
    ACTIVE("激活"),
    
    /**
     * 非激活状态
     */
    INACTIVE("非激活"),
    
    /**
     * 锁定状态
     */
    LOCKED("锁定"),
    
    /**
     * 删除状态
     */
    DELETED("删除");
    
    private final String description;
    
    UserStatus(String description) {
        this.description = description;
    }
    
    public String getDescription() {
        return description;
    }
}
