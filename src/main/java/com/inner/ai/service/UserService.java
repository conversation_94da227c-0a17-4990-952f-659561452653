package com.inner.ai.service;

import com.inner.ai.dto.request.UserPageRequest;
import com.inner.ai.dto.request.UserRequest;
import com.inner.ai.dto.response.UserResponse;
import com.inner.ai.enums.UserStatus;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * 用户服务接口
 */
public interface UserService {
    
    /**
     * 创建用户
     */
    UserResponse createUser(UserRequest request);

    /**
     * 根据ID获取用户
     */
    UserResponse getUserById(Long id);

    /**
     * 更新用户信息
     */
    UserResponse updateUser(Long id, UserRequest request);
    
    /**
     * 删除用户（逻辑删除）
     */
    void deleteUser(Long id);
    
    /**
     * 分页查询用户
     */
    Page<UserResponse> getUserPage(UserPageRequest request);
    
    /**
     * 根据状态获取用户列表
     */
    List<UserResponse> getUsersByStatus(UserStatus status);
    
    /**
     * 根据邮箱获取用户
     */
    UserResponse getUserByEmail(String email);
    
    /**
     * 检查邮箱是否存在
     */
    boolean existsByEmail(String email);
    
    /**
     * 检查邮箱是否存在（排除指定ID）
     */
    boolean existsByEmailAndIdNot(String email, Long excludeId);
    
    /**
     * 激活用户
     */
    UserResponse activateUser(Long id);
    
    /**
     * 锁定用户
     */
    UserResponse lockUser(Long id);
}
