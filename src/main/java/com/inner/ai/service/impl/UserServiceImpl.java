package com.inner.ai.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.inner.ai.common.ExceptionEnum;
import com.inner.ai.dto.request.UserPageRequest;
import com.inner.ai.dto.request.UserRequest;
import com.inner.ai.dto.response.UserResponse;
import com.inner.ai.entity.User;
import com.inner.ai.enums.UserStatus;
import com.inner.ai.exception.BusinessException;
import com.inner.ai.repository.UserRepository;
import com.inner.ai.service.UserService;
import jakarta.persistence.criteria.Predicate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 用户服务实现类
 */
@Slf4j
@Service
@Transactional(readOnly = true)
public class UserServiceImpl implements UserService {
    
    private final UserRepository userRepository;
    
    public UserServiceImpl(UserRepository userRepository) {
        this.userRepository = userRepository;
    }
    
    @Override
    @Transactional
    public UserResponse createUser(UserRequest request) {
        log.info("开始创建用户，邮箱: {}", request.getEmail());

        // 校验邮箱是否已存在
        validateEmailNotExists(request.getEmail());

        User user = User.builder()
                .name(request.getName())
                .email(request.getEmail())
                .phone(request.getPhone())
                .status(ObjectUtil.defaultIfNull(request.getStatus(), UserStatus.ACTIVE))
                .build();

        User savedUser = userRepository.save(user);
        log.info("用户创建成功，ID: {}", savedUser.getId());

        return UserResponse.fromEntity(savedUser);
    }
    
    @Override
    public UserResponse getUserById(Long id) {
        log.info("根据ID获取用户: {}", id);

        User user = findUserByIdOrThrow(id);
        return UserResponse.fromEntity(user);
    }
    
    @Override
    @Transactional
    public UserResponse updateUser(Long id, UserRequest request) {
        log.info("开始更新用户，ID: {}", id);

        User user = findUserByIdOrThrow(id);

        // 校验邮箱是否被其他用户使用
        validateEmailNotExistsForUpdate(request.getEmail(), user.getEmail(), id);

        // 更新字段
        updateUserFields(user, request);

        User updatedUser = userRepository.save(user);
        log.info("用户更新成功，ID: {}", updatedUser.getId());

        return UserResponse.fromEntity(updatedUser);
    }
    
    @Override
    @Transactional
    public void deleteUser(Long id) {
        log.info("开始删除用户，ID: {}", id);

        User user = findUserByIdOrThrow(id);
        updateUserStatus(user, UserStatus.DELETED);

        log.info("用户删除成功，ID: {}", id);
    }
    
    @Override
    public Page<UserResponse> getUserPage(UserPageRequest request) {
        log.info("分页查询用户，页码: {}, 大小: {}", request.getPage(), request.getSize());
        
        // 构建排序
        Sort sort = buildSort(request.getSortBy(), request.getSortDirection());
        Pageable pageable = PageRequest.of(request.getPage(), request.getSize(), sort);
        
        // 构建查询条件
        Specification<User> spec = buildUserSpecification(request);
        
        Page<User> userPage = userRepository.findAll(spec, pageable);
        
        return userPage.map(UserResponse::fromEntity);
    }
    
    @Override
    public List<UserResponse> getUsersByStatus(UserStatus status) {
        log.info("根据状态查询用户: {}", status);
        
        return userRepository.findByStatus(status)
                .stream()
                .map(UserResponse::fromEntity)
                .toList();
    }
    
    @Override
    public UserResponse getUserByEmail(String email) {
        log.info("根据邮箱获取用户: {}", email);

        User user = userRepository.findByEmail(email)
                .orElseThrow(() -> new BusinessException(ExceptionEnum.USER_NOT_FOUND));

        return UserResponse.fromEntity(user);
    }
    
    @Override
    public boolean existsByEmail(String email) {
        return userRepository.findByEmail(email).isPresent();
    }
    
    @Override
    public boolean existsByEmailAndIdNot(String email, Long excludeId) {
        return userRepository.existsByEmailAndIdNot(email, excludeId);
    }
    
    @Override
    @Transactional
    public UserResponse activateUser(Long id) {
        log.info("激活用户，ID: {}", id);

        User user = findUserByIdOrThrow(id);
        User updatedUser = updateUserStatus(user, UserStatus.ACTIVE);

        log.info("用户激活成功，ID: {}", id);
        return UserResponse.fromEntity(updatedUser);
    }

    @Override
    @Transactional
    public UserResponse lockUser(Long id) {
        log.info("锁定用户，ID: {}", id);

        User user = findUserByIdOrThrow(id);
        User updatedUser = updateUserStatus(user, UserStatus.LOCKED);

        log.info("用户锁定成功，ID: {}", id);
        return UserResponse.fromEntity(updatedUser);
    }
    
    /**
     * 构建排序
     */
    private Sort buildSort(String sortBy, String sortDirection) {
        Sort.Direction direction = "asc".equalsIgnoreCase(sortDirection) 
                ? Sort.Direction.ASC 
                : Sort.Direction.DESC;
        return Sort.by(direction, sortBy);
    }
    
    /**
     * 根据ID查找用户，不存在则抛出异常
     */
    private User findUserByIdOrThrow(Long id) {
        return userRepository.findById(id)
                .orElseThrow(() -> new BusinessException(ExceptionEnum.USER_NOT_FOUND));
    }

    /**
     * 校验邮箱是否已存在
     */
    private void validateEmailNotExists(String email) {
        if (userRepository.findByEmail(email).isPresent()) {
            throw new BusinessException(ExceptionEnum.USER_EMAIL_EXISTS);
        }
    }

    /**
     * 校验邮箱是否被其他用户使用（更新时使用）
     */
    private void validateEmailNotExistsForUpdate(String newEmail, String currentEmail, Long userId) {
        if (StrUtil.isNotBlank(newEmail) &&
            !newEmail.equals(currentEmail) &&
            userRepository.existsByEmailAndIdNot(newEmail, userId)) {
            throw new BusinessException(ExceptionEnum.USER_EMAIL_EXISTS);
        }
    }

    /**
     * 更新用户字段
     */
    private void updateUserFields(User user, UserRequest request) {
        if (StrUtil.isNotBlank(request.getName())) {
            user.setName(request.getName());
        }
        if (StrUtil.isNotBlank(request.getEmail())) {
            user.setEmail(request.getEmail());
        }
        if (StrUtil.isNotBlank(request.getPhone())) {
            user.setPhone(request.getPhone());
        }
        if (BeanUtil.isNotEmpty(request.getStatus())) {
            user.setStatus(request.getStatus());
        }
    }

    /**
     * 更新用户状态并保存
     */
    private User updateUserStatus(User user, UserStatus status) {
        user.setStatus(status);
        return userRepository.save(user);
    }

    /**
     * 构建用户查询条件
     */
    private Specification<User> buildUserSpecification(UserPageRequest request) {
        return (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            // 用户名模糊查询
            if (StrUtil.isNotBlank(request.getName())) {
                predicates.add(criteriaBuilder.like(
                        criteriaBuilder.lower(root.get("name")),
                        "%" + request.getName().toLowerCase() + "%"
                ));
            }

            // 邮箱精确查询
            if (StrUtil.isNotBlank(request.getEmail())) {
                predicates.add(criteriaBuilder.equal(root.get("email"), request.getEmail()));
            }

            // 状态查询
            if (BeanUtil.isNotEmpty(request.getStatus())) {
                predicates.add(criteriaBuilder.equal(root.get("status"), request.getStatus()));
            }

            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
    }
}
