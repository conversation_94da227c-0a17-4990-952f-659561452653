server:
  port: 8080
  servlet:
    context-path: /api

spring:
  application:
    name: inner-ai
  profiles:
    active: local

  # 数据源配置
  datasource:
    url: **************************************************************************************************************************
    username: postgres
    password: 123456
    driver-class-name: org.postgresql.Driver
    type: com.zaxxer.hikari.HikariDataSource
    hikari:
      minimum-idle: 5
      maximum-pool-size: 20
      auto-commit: true
      idle-timeout: 30000
      pool-name: InnerAiHikariCP
      max-lifetime: 1800000
      connection-timeout: 30000
      connection-test-query: SELECT 1

  # Jackson配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: Asia/Shanghai
    default-property-inclusion: non_null

  # JPA配置
  jpa:
    hibernate:
      ddl-auto: validate  # 验证数据库结构，不自动创建表
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
        use_sql_comments: true
        jdbc:
          time_zone: Asia/Shanghai

  # Flyway数据库迁移配置
  flyway:
    enabled: true
    locations: classpath:db/migration
    baseline-on-migrate: true
    validate-on-migrate: true
    clean-disabled: true

  # SQL初始化配置
  sql:
    init:
      mode: never  # 禁用自动DDL执行

  # Redis配置
  data:
    redis:
      host: localhost
      port: 6379
      password:
      database: 0
      timeout: 3000ms
      lettuce:
        pool:
          max-active: 8
          max-idle: 8
          min-idle: 0
          max-wait: -1ms


# 日志配置
logging:
  level:
    com.inner.ai: debug
    org.springframework.web: info
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# JWT配置
jwt:
  secret: inner-ai-jwt-secret-key-2024-very-long-and-secure-key-for-production-use
  expiration: 2592000000  # JWT token过期时间：30天（毫秒），足够长，主要靠Redis控制会话
  redis-expiration: 3600  # Redis会话过期时间：1小时（秒），用户活跃时自动延长
  refresh-threshold: 600  # 刷新阈值：最后10分钟可刷新（秒）
  issuer: inner-ai-system
  header: Authorization
  prefix: "Bearer "

# Knife4j和SpringDoc配置
springdoc:
  api-docs:
    enabled: true
    path: /v3/api-docs
  swagger-ui:
    enabled: true
    path: /swagger-ui.html
  info:
    title: Inner AI 系统API文档
    description: 基于Spring Boot 3.x + JPA + PostgreSQL的AI系统后端API
    version: 1.0.0
    contact:
      name: Inner AI Team
      email: <EMAIL>
      url: https://github.com/inner-ai
    license:
      name: MIT License
      url: https://opensource.org/licenses/MIT
  servers:
    - url: http://localhost:8080/api
      description: 本地开发环境
    - url: https://api.inner-ai.com
      description: 生产环境

# Knife4j增强配置
knife4j:
  enable: true
  setting:
    language: zh_cn                              # 中文界面
    enable-version: true                         # 启用版本管理
    enable-swagger-models: true                  # 启用模型展示
    enable-document-manage: true                 # 启用文档管理
    swagger-model-name: 实体类列表               # 模型列表名称
    enable-host: false                           # 禁用Host设置
    enable-request-cache: true                   # 启用请求缓存
    enable-filter-multipart-api-method-type: POST # 过滤文件上传接口的方法类型
    enable-filter-multipart-apis: false         # 不过滤文件上传接口
    enable-after-script: true                   # 启用调试后脚本
    enable-response-code: true                  # 启用响应状态码
    enable-search: true                         # 启用搜索功能
    enable-footer: false                        # 禁用页脚
    enable-footer-custom: false                 # 禁用自定义页脚
    enable-home-custom: false                   # 禁用自定义首页



