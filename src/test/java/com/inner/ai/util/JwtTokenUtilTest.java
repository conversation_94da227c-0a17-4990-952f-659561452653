package com.inner.ai.util;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;

/**
 * JWT Token工具类测试
 * 
 * <AUTHOR> AI Team
 * @since 1.0.0
 */
@SpringBootTest
@TestPropertySource(properties = {
        "jwt.secret=test-secret-key-for-jwt-token-generation-and-validation-very-long-key",
        "jwt.expiration=86400000",
        "jwt.refresh-expiration=604800000",
        "jwt.issuer=inner-ai-test"
})
class JwtTokenUtilTest {
    
    private Long testUserId;
    private String testUsername;
    
    @BeforeEach
    void setUp() {
        testUserId = 1L;
        testUsername = "testuser";
    }
    
    @Test
    void testGenerateAccessToken() {
        // 生成访问令牌
        String accessToken = JwtTokenUtil.generateAccessToken(testUserId, testUsername);
        
        assertNotNull(accessToken);
        assertFalse(accessToken.isEmpty());
        
        // 验证Token格式（JWT应该有三个部分，用.分隔）
        String[] parts = accessToken.split("\\.");
        assertEquals(3, parts.length, "JWT应该有三个部分");
    }
    
    @Test
    void testGenerateRefreshToken() {
        // 生成刷新令牌
        String refreshToken = JwtTokenUtil.generateRefreshToken(testUserId, testUsername);
        
        assertNotNull(refreshToken);
        assertFalse(refreshToken.isEmpty());
        
        // 验证Token格式
        String[] parts = refreshToken.split("\\.");
        assertEquals(3, parts.length, "JWT应该有三个部分");
    }
    
    @Test
    void testValidateToken() {
        // 生成Token
        String token = JwtTokenUtil.generateAccessToken(testUserId, testUsername);
        
        // 验证Token
        assertTrue(JwtTokenUtil.validateToken(token), "生成的Token应该是有效的");
        
        // 验证无效Token
        assertFalse(JwtTokenUtil.validateToken("invalid.token.here"), "无效Token应该验证失败");
        assertFalse(JwtTokenUtil.validateToken(null), "null Token应该验证失败");
        assertFalse(JwtTokenUtil.validateToken(""), "空Token应该验证失败");
    }
    
    @Test
    void testGetUserIdFromToken() {
        // 生成Token
        String token = JwtTokenUtil.generateAccessToken(testUserId, testUsername);
        
        // 从Token中获取用户ID
        Long extractedUserId = JwtTokenUtil.getUserIdFromToken(token);
        
        assertNotNull(extractedUserId);
        assertEquals(testUserId, extractedUserId);
        
        // 测试无效Token
        assertNull(JwtTokenUtil.getUserIdFromToken("invalid.token.here"));
    }
    
    @Test
    void testGetUsernameFromToken() {
        // 生成Token
        String token = JwtTokenUtil.generateAccessToken(testUserId, testUsername);
        
        // 从Token中获取用户名
        String extractedUsername = JwtTokenUtil.getUsernameFromToken(token);
        
        assertNotNull(extractedUsername);
        assertEquals(testUsername, extractedUsername);
        
        // 测试无效Token
        assertNull(JwtTokenUtil.getUsernameFromToken("invalid.token.here"));
    }
    
    @Test
    void testGetExpirationFromToken() {
        // 生成Token
        String token = JwtTokenUtil.generateAccessToken(testUserId, testUsername);
        
        // 从Token中获取过期时间
        LocalDateTime expiration = JwtTokenUtil.getExpirationFromToken(token);
        
        assertNotNull(expiration);
        assertTrue(expiration.isAfter(LocalDateTime.now()), "过期时间应该在未来");
        
        // 测试无效Token
        assertNull(JwtTokenUtil.getExpirationFromToken("invalid.token.here"));
    }
    
    @Test
    void testGetTokenInfo() {
        // 生成Token
        String token = JwtTokenUtil.generateAccessToken(testUserId, testUsername);
        
        // 获取Token信息
        JwtTokenUtil.TokenInfo tokenInfo = JwtTokenUtil.getTokenInfo(token);
        
        assertNotNull(tokenInfo);
        assertEquals(testUserId, tokenInfo.getUserId());
        assertEquals(testUsername, tokenInfo.getUsername());
        assertNotNull(tokenInfo.getExpireTime());
        assertTrue(tokenInfo.getExpireTime().isAfter(LocalDateTime.now()));
        
        // 测试无效Token
        assertNull(JwtTokenUtil.getTokenInfo("invalid.token.here"));
    }
    
    @Test
    void testIsTokenExpiringSoon() {
        // 生成Token
        String token = JwtTokenUtil.generateAccessToken(testUserId, testUsername);
        
        // 检查Token是否即将过期（24小时的Token不应该在1小时内过期）
        assertFalse(JwtTokenUtil.isTokenExpiringSoon(token), "新生成的Token不应该即将过期");
        
        // 测试无效Token
        assertTrue(JwtTokenUtil.isTokenExpiringSoon("invalid.token.here"), "无效Token应该被认为即将过期");
    }
    
    @Test
    void testTokenConsistency() {
        // 生成两个不同的Token
        String token1 = JwtTokenUtil.generateAccessToken(testUserId, testUsername);
        String token2 = JwtTokenUtil.generateAccessToken(testUserId, testUsername);
        
        // Token应该不同（因为包含时间戳）
        assertNotEquals(token1, token2, "不同时间生成的Token应该不同");
        
        // 但是从中提取的用户信息应该相同
        assertEquals(JwtTokenUtil.getUserIdFromToken(token1), JwtTokenUtil.getUserIdFromToken(token2));
        assertEquals(JwtTokenUtil.getUsernameFromToken(token1), JwtTokenUtil.getUsernameFromToken(token2));
    }
}
