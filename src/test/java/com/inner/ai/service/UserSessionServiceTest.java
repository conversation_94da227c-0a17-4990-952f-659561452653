package com.inner.ai.service;

import com.inner.ai.service.impl.UserSessionServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.test.context.TestPropertySource;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * 用户会话服务测试
 * 
 * <AUTHOR> AI Team
 * @since 1.0.0
 */
@SpringBootTest
@TestPropertySource(properties = {
        "spring.data.redis.host=localhost",
        "spring.data.redis.port=6379"
})
class UserSessionServiceTest {
    
    private UserSessionService userSessionService;
    private RedisTemplate<String, Object> redisTemplate;
    
    private final String testToken = "test-jwt-token-12345";
    private final Long testUserId = 1L;
    private final String testUsername = "testuser";
    
    @BeforeEach
    void setUp() {
        // 使用Mock Redis Template进行测试
        redisTemplate = mock(RedisTemplate.class);
        userSessionService = new UserSessionServiceImpl(redisTemplate);
    }
    
    @Test
    void testCreateSession() {
        // 测试创建会话
        long expireSeconds = 3600; // 1小时
        
        // 模拟Redis操作
        when(redisTemplate.opsForValue()).thenReturn(mock(org.springframework.data.redis.core.ValueOperations.class));
        when(redisTemplate.opsForSet()).thenReturn(mock(org.springframework.data.redis.core.SetOperations.class));
        
        // 执行测试
        assertDoesNotThrow(() -> {
            userSessionService.createSession(testToken, testUserId, testUsername, expireSeconds);
        });
        
        // 验证Redis操作被调用
        verify(redisTemplate.opsForValue(), times(1)).set(anyString(), any(), eq(expireSeconds), any());
        verify(redisTemplate.opsForSet(), times(1)).add(anyString(), eq(testToken));
    }
    
    @Test
    void testIsSessionValid() {
        // 模拟Redis操作
        when(redisTemplate.hasKey(anyString())).thenReturn(true);
        
        // 测试会话有效性检查
        boolean isValid = userSessionService.isSessionValid(testToken);
        
        assertTrue(isValid);
        verify(redisTemplate, times(1)).hasKey(anyString());
    }
    
    @Test
    void testIsSessionInvalid() {
        // 模拟Redis操作 - 会话不存在
        when(redisTemplate.hasKey(anyString())).thenReturn(false);
        
        // 测试会话有效性检查
        boolean isValid = userSessionService.isSessionValid(testToken);
        
        assertFalse(isValid);
        verify(redisTemplate, times(1)).hasKey(anyString());
    }
    
    @Test
    void testRemoveSession() {
        // 模拟获取会话信息
        UserSessionService.UserSession mockSession = new UserSessionService.UserSession(testUserId, testUsername);
        when(redisTemplate.opsForValue()).thenReturn(mock(org.springframework.data.redis.core.ValueOperations.class));
        when(redisTemplate.opsForValue().get(anyString())).thenReturn(mockSession);
        when(redisTemplate.getExpire(anyString())).thenReturn(3600L);
        when(redisTemplate.opsForSet()).thenReturn(mock(org.springframework.data.redis.core.SetOperations.class));
        when(redisTemplate.delete(anyString())).thenReturn(true);
        
        // 测试删除会话
        boolean removed = userSessionService.removeSession(testToken);
        
        assertTrue(removed);
        verify(redisTemplate, times(1)).delete(anyString());
    }
    
    @Test
    void testGetUserActiveSessionCount() {
        // 模拟Redis操作
        when(redisTemplate.opsForSet()).thenReturn(mock(org.springframework.data.redis.core.SetOperations.class));
        when(redisTemplate.opsForSet().size(anyString())).thenReturn(3L);
        
        // 测试获取用户活跃会话数量
        int count = userSessionService.getUserActiveSessionCount(testUserId);
        
        assertEquals(3, count);
        verify(redisTemplate.opsForSet(), times(1)).size(anyString());
    }
    
    @Test
    void testRefreshSession() {
        // 模拟Redis操作
        when(redisTemplate.hasKey(anyString())).thenReturn(true);
        when(redisTemplate.expire(anyString(), anyLong(), any())).thenReturn(true);
        
        // 测试刷新会话
        boolean refreshed = userSessionService.refreshSession(testToken, 7200);
        
        assertTrue(refreshed);
        verify(redisTemplate, times(1)).expire(anyString(), eq(7200L), any());
    }
    
    @Test
    void testRefreshNonExistentSession() {
        // 模拟Redis操作 - 会话不存在
        when(redisTemplate.hasKey(anyString())).thenReturn(false);
        
        // 测试刷新不存在的会话
        boolean refreshed = userSessionService.refreshSession(testToken, 7200);
        
        assertFalse(refreshed);
        verify(redisTemplate, never()).expire(anyString(), anyLong(), any());
    }
}
